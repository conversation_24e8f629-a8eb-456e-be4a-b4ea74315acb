{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedAdmin.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "html-pdf-node": "^1.0.8", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "mongoose": "^8.15.1", "multer": "^2.0.1", "node-cron": "^4.1.0", "nodemailer": "^7.0.3", "prop-types": "^15.8.1", "puppeteer": "^24.10.0", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}